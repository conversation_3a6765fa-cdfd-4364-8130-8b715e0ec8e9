<template>
  <el-dialog title="全链路" v-model="allChainVisibleEp" fullscreen class="position-dialog">
    <template #header> <el-button type="primary" @click="closeTraceLogsEp" :icon="Back">返回</el-button><Strong></Strong>
    </template>
    <EvalResultPosition ref="experiencePositionRef"></EvalResultPosition>
  </el-dialog>
  <page-wrapper route-name="evaluation-reslut::">
    <div class="result">
      <el-card class="table-card">
        <div class="evaluation-task-table">
          <table-page ref="myTableRef" :columns="columns" :loadDataApi="loadListData" :operations="operations"
            @operation="handleOperation" :transformListData="transformListData" :query="query">
            <template #query>
              <div class="flexBetweenStart">
                <my-query :queryItems="queryItems" :refreshBtn="{ show: true }" @search="events.search"
                  @reset="events.reset" />

                <my-button type="export" @click="events.exportExcel" style="margin-left: 20px">导出</my-button>
              </div>
            </template>

            <template #traceId-slot="{ row }">
              <div class="flex items-center">
                <span>{{ row.traceId || '-' }}</span>
                <el-icon class="icon-copy ml-2" v-if="row.traceId" @click="copyText(row.traceId)" title="复制">
                  <CopyDocument />
                </el-icon>
              </div>
            </template>


            <template #docId-slot="{ row }">
              <div class="flex items-center">
                <span>{{ row.docId || '-' }}</span>
                <el-icon class="icon-copy ml-2" v-if="row.docId" @click="copyText(row.docId)" title="复制">
                  <CopyDocument />
                </el-icon>
              </div>
            </template>

            <template #title-slot="{ row }">
              <div class="flex items-center">
                <span>{{ row.title || '-' }}</span>
                <el-icon class="icon-copy ml-2" v-if="row.title" @click="copyText(row.title)" title="复制">
                  <CopyDocument />
                </el-icon>
              </div>
            </template>
          </table-page>
        </div>
      </el-card>
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, nextTick } from "vue";
import { timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import * as util from "@/utils/common";
import { assign } from "lodash";
const { $router, proxy, $app } = useCtx();
import * as evalManageApi from "@/api/eval-manage";
import * as markApi from "@/api/eval-mark";
import * as commonApi from "@/api/common";
import EvalResultPosition from "@/views/evaluation-manage/result/EvalResultPosition.vue";
import { copyText } from "@/utils/helpers";
const routeQuery = $app.$route.query;
const query = ref<any>({});
const columns = ref<any[]>([]);
const allChainVisibleEp = ref(false);

const queryItems = ref<any>({
  search: {
    type: "input",
    label: "",
    width: "220px",
    modelValue: "",
    attrs: {
      placeholder: "标注人 或 Query 或 策略名称",
    },
  },

  time: {
    type: "datetimerange",
    label: "",
    modelValue: "",
    width: "250px",
    collapsed: true,
    attrs: {
      placeholder: "请输入备注",
    },
  },
});

//列配置
const defaultColumns = ref([
  {
    prop: "sceneProcessName",
    label: "策略名称",
    minWidth: 240,
  },
  {
    prop: "regionName",
    label: "环境",
    minWidth: 120,
  },
  {
    prop: "query",
    label: "query",
    minWidth: 190,
    blod: true,
    custom: "link",
    customRender: {
      click: (record: any) => {
        events.allChain(record);
      },
    },
  },
  {
    prop: "account",
    label: "标注人",
    minWidth: 120,
  },
  {
    prop: "lastModifiedDateRender",
    label: "标注时间",
    minWidth: 180,
  },
  {
    prop: "traceId",
    label: "traceId",
    minWidth: 200,
    slotName: "traceId-slot",
  },
  {
    prop: "docId",
    label: "docId",
    minWidth: 220,
    slotName: "docId-slot",
  },
  {
    prop: "url",
    label: "URL",
    minWidth: 200,
    blod: true,
    custom: "link",
    customRender: {
      click: (record: any) => {
        window.open(record.url, "_blank")
      },
    },
  },
  {
    prop: "title",
    label: "标题",
    minWidth: 200,
    slotName: "title-slot",
  },
  { prop: "operation", label: "操作", width: 80, fixed: "right" },
]);

//列表查询
const handleParams = (params: any) => {
  if (params.time) {
    params.startTime = params.time[0]
    params.endTime = params.time[1]
    delete params.time
  }
  return params
}

const loadListData = (data: any) => {
  return new Promise((resolve: any) => {
    let params: any = handleParams(data)

    evalManageApi.getTablePage({ ...params, regionName: routeQuery.regionName, sceneProcessName: routeQuery.sceneProcessName, type: 1 }).then((result) => {
      const arr = util.generateTableColumns(result.content);
      columns.value = defaultColumns.value.concat([...arr] as any);
      result.content = result.content.map((item: any) => ({
        ...item,
        ...item.extendFieldMap,
      }));
      resolve(result);
    });
  });
};

//转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.lastModifiedDateRender = timeC.format(x.lastModifiedDate, "YYYY-MM-DD hh:mm:ss");
    x.docIdxRender = (x.docIdx != undefined && x.type === 0) ? `Top${x.docIdx + 1}` : "-";
    return x;
  });
};

//事件列表
const events = reactive({
  search: (obj: any) => {
    query.value = assign({}, query.value, obj);
  },
  reset: () => { },
  exportExcel: () => {
    evalManageApi
      .getTableExport({ column: routeQuery.column, userType: routeQuery.userType, regionName: routeQuery.regionName, sceneProcessName: routeQuery.sceneProcessName, type: 1 })
      .then((res) =>
        util.downloadFile(
          res,
          `query标注明细.xlsx`
        )
      );
  },
  allChain: async (record: any) => {
    console.log("allChain:", record);
    //阻止原生路由跳转事件
    event.preventDefault();
    //修改当前路由
    const curRouteName = $router.currentRoute.value.name;
    await $router.push({ name: `${curRouteName}`, query: getAllChainToQuery(record) });
    //当归因模式是自研badcase分析，可以跳转全链路
    //打开一个全屏dialog
    allChainVisibleEp.value = true;
    //刷新全链路
    nextTick(() => {
      proxy.$refs.experiencePositionRef.getTraceinfo();
    });
  },
  delete: (record: any) => {
    $app
      .$deleteConfirm({
        title: `您确认要删除?`,
      })
      .then(() => {
        markApi.deleteMarkResult(record.resultId).then((result) => {
          loadList();
          $app.$message.success(`删除 ${record.name} 成功`);
        });
      });
  },
});

function closeTraceLogsEp() {
  allChainVisibleEp.value = false;
  $router.push({ name: `evaluation-reslut` });
  loadList();
}

//获取全链路跳转的query参数
const getAllChainToQuery = (record: any) => {
  let newQuery = {};
  newQuery = assign(newQuery, {
    mode: "ceping",
    searchId: record.docId,
    url: record.url,
    markRecordId: record.markRecordId,
    tag: record.metadata.tag,
  });
  return newQuery;
};


//操作
const operations = [
  {
    type: "delete",
    label: "删除",
    btnType: "danger",
  }
];
const handleOperation = (data: any) => {
  const { type, record } = data;
  typeof events[type] == "function" && events[type](record);
};


const loadList = () => {
  proxy.$refs["myTableRef"]?.loadData();
};

const anasisList = ref([]);
// 场景策略
const getAnasisList = async (name = "") => {
  const res = await commonApi.getSceneVersion({ name });
  anasisList.value = res.data.map((item: any) => ({
    ...item,
    label: `${item.name}(v${util.padNumberToDigits(item.version, 3)})`,
    value: item.processId,
  }));
};

//初始化
onMounted(async () => {
  getAnasisList();
});

//接口暴露
defineExpose({
  loadList,
  loadListData,
});
</script>
<style lang="scss">
.result {
  padding: 10px;

  .table-card {
    height: calc(100vh - 120px);

    .el-card__body {
      height: 100%;
    }

    .no-bottom {
      margin-bottom: 0;
    }
  }
}

.evaluation-task-table {
  height: calc(100% - 40px);

  ::v-deep {
    .query-wrapper {
      padding: 0 !important;
    }

    .table-wrapper {
      padding: 0 !important;
    }
  }
}

::v-deep(.el-radio-group) {
  margin-bottom: 14px;
}

.total-info,
.inline-block {
  display: inline-block;
}

.name+.name {
  margin-left: 15px;
}

.icon-copy {
  cursor: pointer;
  color: #409eff;
  margin-left: 8px;

  &:hover {
    color: #66b1ff;
  }
}

.flex {
  display: flex;
  align-items: center;
}

.ml-2 {
  margin-left: 8px;
}
</style>